package com.dep.biguo.mvp.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

/**
 * 闲置商品适配器
 */
public class IdleProductAdapter extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

    public IdleProductAdapter(@Nullable List<OrderBean> data) {
        super(R.layout.item_idle_product, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OrderBean item) {
        // 设置商品标题
        if (item.getGoods_data() != null && !item.getGoods_data().isEmpty()) {
            ShopBean firstGood = item.getGoods_data().get(0);
            helper.setText(R.id.tvProductTitle, firstGood.getName());

            // 设置价格
            String priceText = "¥" + firstGood.getPrice();
            helper.setText(R.id.tvPrice, priceText);

            // 加载商品图片
            ImageView ivProductImage = helper.getView(R.id.ivProductImage);
            if (!TextUtils.isEmpty(firstGood.getImg())) {
                ImageLoader.loadImage(ivProductImage, firstGood.getImg());
            } else {
                ivProductImage.setImageResource(R.drawable.icon_book);
            }
        }

        // 设置状态和操作按钮
        setupStatusAndAction(helper, item);
    }

    /**
     * 设置状态标签和操作按钮
     */
    private void setupStatusAndAction(BaseViewHolder helper, OrderBean item) {
        TextView tvStatusTag = helper.getView(R.id.tvStatusTag);
        TextView btnAction = helper.getView(R.id.btnAction);

        switch (item.getState()) {
            case 1: // 在卖
                tvStatusTag.setText("新评论+1");
                tvStatusTag.setVisibility(View.VISIBLE);
                btnAction.setText("下架");
                break;
            case 2: // 待发货
                tvStatusTag.setText("买家已付款");
                tvStatusTag.setVisibility(View.VISIBLE);
                btnAction.setText("发货");
                break;
            case 3: // 已完成
                tvStatusTag.setVisibility(View.GONE);
                btnAction.setText("再次发布");
                break;
            default:
                tvStatusTag.setText("在售中");
                tvStatusTag.setVisibility(View.VISIBLE);
                btnAction.setText("下架");
                break;
        }

        // 设置按钮点击事件
        btnAction.setOnClickListener(v -> {
            // 处理按钮点击事件
            handleActionClick(item, helper.getAdapterPosition());
        });
    }

    /**
     * 处理操作按钮点击事件
     */
    private void handleActionClick(OrderBean item, int position) {
        switch (item.getState()) {
            case 1: // 在卖 - 下架
                // TODO: 实现下架逻辑
                break;
            case 2: // 待发货 - 发货
                // TODO: 实现发货逻辑
                break;
            case 3: // 已完成 - 再次发布
                // TODO: 实现再次发布逻辑
                break;
        }
    }
}
