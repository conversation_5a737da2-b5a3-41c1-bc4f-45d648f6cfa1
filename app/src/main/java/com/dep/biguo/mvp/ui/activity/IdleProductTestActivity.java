package com.dep.biguo.mvp.ui.activity;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.mvp.ui.adapter.IdleProductAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 闲置商品测试Activity
 * 用于展示三种不同的状态显示
 */
public class IdleProductTestActivity extends AppCompatActivity {

    private RecyclerView recyclerView;
    private IdleProductAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_idle_product_test);

        initViews();
        initData();
    }

    private void initViews() {
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        
        adapter = new IdleProductAdapter(new ArrayList<>());
        recyclerView.setAdapter(adapter);
    }

    private void initData() {
        List<OrderBean> testData = new ArrayList<>();

        // 状态1：隐藏btnAction2，将btnAction文案改成"下架"
        OrderBean item1 = createTestItem("马克思原理二", "16.00", 1);
        testData.add(item1);

        // 状态2：隐藏tvStatusTag
        OrderBean item2 = createTestItem("高等数学教材", "25.00", 2);
        testData.add(item2);

        // 状态3：隐藏btnAction2和tvStatusTag
        OrderBean item3 = createTestItem("英语四级词汇", "18.50", 3);
        testData.add(item3);

        adapter.setNewData(testData);
    }

    /**
     * 创建测试数据项
     */
    private OrderBean createTestItem(String name, String price, int state) {
        OrderBean item = new OrderBean();
        item.setOrder_id(1000 + state);
        item.setName(name);
        item.setType("idle");
        item.setState(state);
        item.setTotal_fee(price);

        // 创建商品数据
        List<ShopBean> goodsData = new ArrayList<>();
        ShopBean shopBean = new ShopBean();
        shopBean.setName(name);
        shopBean.setPrice(price);
        shopBean.setImg(""); // 使用默认图片
        goodsData.add(shopBean);
        item.setGoods_data(goodsData);

        return item;
    }
}
