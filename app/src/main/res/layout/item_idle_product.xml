<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:padding="16dp"
    android:layout_marginBottom="10dp">

    <!-- 商品信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 商品图片 -->
        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_book"
            android:background="@drawable/bg_round_8_white"/>

        <!-- 商品信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <!-- 商品标题 -->
            <TextView
                android:id="@+id/tvProductTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="马克思原理二"
                android:textColor="@color/tblack"
                android:textSize="16sp"
                android:textStyle="normal"
                android:maxLines="2"
                android:ellipsize="end"/>

            <!-- 价格 -->
            <TextView
                android:id="@+id/tvPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="¥16.00"
                android:textColor="@color/tblack"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </LinearLayout>

    </LinearLayout>
    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="16dp"
        android:background="#EBEBEB" />
    <!-- 底部操作区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 状态标签 -->
        <TextView
            android:id="@+id/tvStatusTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="新评论+1"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:background="@drawable/bg_round_10_theme"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"/>

        <!-- 占位空间 -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1"/>
        <TextView
            android:layout_marginEnd="10dp"
            android:id="@+id/btnAction2"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="下架"
            android:textColor="@color/theme"
            android:textSize="14sp"
            android:background="@drawable/bg_btn_outline_red"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:gravity="center"/>
        <!-- 操作按钮 -->
        <TextView
            android:id="@+id/btnAction"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="下架"
            android:textColor="@color/theme"
            android:textSize="14sp"
            android:background="@drawable/bg_btn_outline_red"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:gravity="center"/>

    </LinearLayout>

</LinearLayout>
