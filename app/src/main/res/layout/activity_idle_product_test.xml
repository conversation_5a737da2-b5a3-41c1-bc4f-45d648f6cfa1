<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/bgc">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="闲置商品状态测试"
            android:textColor="@color/tblack"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"/>

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#EBEBEB"/>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="以下展示三种不同的状态：\n1. 隐藏btnAction2，btnAction显示"下架"\n2. 隐藏tvStatusTag\n3. 隐藏btnAction2和tvStatusTag"
        android:textColor="@color/black3"
        android:textSize="14sp"
        android:background="@color/white"
        android:layout_marginBottom="10dp"/>

    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgc"/>

</LinearLayout>
